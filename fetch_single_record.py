import braintrust
import os

def fetch_single_dataset_record(project_name: str, dataset_name: str):
    """
    Fetch exactly one record from a dataset using direct BTQL API call.
    
    Args:
        project_name: Name of the project
        dataset_name: Name of the dataset
    
    Returns:
        Single dataset record or None if no records exist
    """
    # Get the dataset metadata to get the dataset ID
    # dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
    # dataset_id = dataset.id
    dataset_id = 'b74f71e9-9a09-47e5-95ca-0db7d0f055a2'
    
    # Get API connection from braintrust state
    state = braintrust.logger._state
    
    # Make direct BTQL API call with limit=1
    resp = state.api_conn().post(
        "btql",
        json={
            "query": {
                "select": [{"op": "star"}],
                "from": {
                    "op": "function",
                    "name": {
                        "op": "ident",
                        "name": ["dataset"],
                    },
                    "args": [
                        {
                            "op": "literal",
                            "value": dataset_id,
                        },
                    ],
                },
                "limit": 1,  # This will be respected!
            },
            "use_columnstore": False,
            "brainstore_realtime": True,
        },
        headers={
            "Accept-Encoding": "gzip",
        },
    )
    
    braintrust.logger.response_raise_for_status(resp)
    result = resp.json()
    
    records = result.get("data", [])
    return records[0] if records else None

# Example usage
if __name__ == "__main__":
    record = fetch_single_dataset_record("pedro-project1", "themes")
    if record:
        print(record)
    else:
        print("No records found")
